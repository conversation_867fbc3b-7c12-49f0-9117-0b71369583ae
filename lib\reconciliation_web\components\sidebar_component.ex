defmodule ReconciliationWeb.SidebarComponent do
  use ReconciliationWeb, :live_component

  alias Reconciliation.Accounts.User
  alias Reconciliation.UserManagement

  def mount(socket) do
    {:ok, socket}
  end

  def update(assigns, socket) do
    # Only fetch pending runs count if it's not already provided or if user changed
    pending_runs_count = case {assigns[:pending_runs_count], assigns[:current_user]} do
      {nil, %{id: user_id}} ->
        # Only fetch if not provided
        Reconciliation.count_pending_reconciliation_runs(user_id)
      {nil, _} ->
        0
      {count, _} ->
        # Use provided count to avoid re-querying
        count
    end

    # Pre-load user roles to avoid repeated database queries
    current_user_with_roles = case assigns[:current_user] do
      %{role_assignments: %Ecto.Association.NotLoaded{}} = user ->
        try do
          Reconciliation.UserManagement.get_user!(user.id)
        rescue
          _ -> user
        end
      user ->
        user
    end

    assigns = assigns
    |> Map.put(:pending_runs_count, pending_runs_count)
    |> Map.put(:current_user_with_roles, current_user_with_roles)

    {:ok, assign(socket, assigns)}
  end

  # Helper function to determine if a navigation item is active
  def nav_class(current_path, target_path, color \\ "probase") do
    base_classes = "group flex items-center px-3 py-2.5 text-sm font-medium rounded-xl transition-all duration-200"

    if String.starts_with?(current_path, target_path) do
      case color do
        "slate" -> "#{base_classes} bg-gray-200 text-gray-800 shadow-sm border border-gray-300"
        "probase" -> "#{base_classes} bg-orange-400 text-white shadow-sm border border-orange-300"
        _ -> "#{base_classes} bg-orange-400 text-white shadow-sm border border-orange-300"
      end
    else
      case color do
        "slate" -> "#{base_classes} hover:bg-gray-100 hover:text-gray-800 text-gray-600 hover:shadow-sm"
        "probase" -> "#{base_classes} hover:bg-orange-50 hover:text-orange-400 text-gray-600 hover:shadow-sm"
        _ -> "#{base_classes} hover:bg-orange-50 hover:text-orange-400 text-gray-600 hover:shadow-sm"
      end
    end
  end

  def icon_class(current_path, target_path, color \\ "probase") do
    base_classes = "w-5 h-5 mr-3 transition-colors"

    if String.starts_with?(current_path, target_path) do
      case color do
        "slate" -> "#{base_classes} text-gray-800"
        "probase" -> "#{base_classes} text-white"
        _ -> "#{base_classes} text-white"
      end
    else
      case color do
        "slate" -> "#{base_classes} text-gray-500 group-hover:text-gray-800"
        "probase" -> "#{base_classes} text-gray-500 group-hover:text-orange-400"
        _ -> "#{base_classes} text-gray-500 group-hover:text-orange-400"
      end
    end
  end

  def active_indicator_class(current_path, target_path, color \\ "probase") do
    base_classes = "ml-auto w-2 h-2 rounded-full transition-opacity"

    if String.starts_with?(current_path, target_path) do
      case color do
        "slate" -> "#{base_classes} bg-slate-500 opacity-100"
        "probase" -> "#{base_classes} bg-probase-secondary opacity-100"
        _ -> "#{base_classes} bg-emerald-500 opacity-100"
      end
    else
      case color do
        "slate" -> "#{base_classes} bg-slate-500 opacity-0 group-hover:opacity-100"
        "probase" -> "#{base_classes} bg-probase-secondary opacity-0 group-hover:opacity-100"
        _ -> "#{base_classes} bg-emerald-500 opacity-0 group-hover:opacity-100"
      end
    end
  end

  # User permission helper functions - optimized to avoid repeated DB queries
  def user_has_role?(assigns, role_name) when is_map(assigns) do
    case assigns[:current_user_with_roles] do
      nil -> false
      user -> User.has_role?(user, role_name)
    end
  end
  def user_has_role?(user, role_name) when is_nil(user), do: false
  def user_has_role?(user, role_name) do
    user_with_roles = load_user_roles(user)
    User.has_role?(user_with_roles, role_name)
  end

  def user_can_manage_users?(assigns) when is_map(assigns) do
    case assigns[:current_user_with_roles] do
      nil -> false
      user ->
        User.has_permission?(user, "users", "read") ||
        User.has_role?(user, "admin") ||
        User.has_role?(user, "manager")
    end
  end
  def user_can_manage_users?(user) when is_nil(user), do: false
  def user_can_manage_users?(user) do
    user_with_roles = load_user_roles(user)
    User.has_permission?(user_with_roles, "users", "read") ||
    User.has_role?(user_with_roles, "admin") ||
    User.has_role?(user_with_roles, "manager")
  end

  def user_has_permission?(assigns, resource, action) when is_map(assigns) do
    case assigns[:current_user_with_roles] do
      nil -> false
      user -> User.has_permission?(user, resource, action)
    end
  end
  def user_has_permission?(user, resource, action) when is_nil(user), do: false
  def user_has_permission?(user, resource, action) do
    user_with_roles = load_user_roles(user)
    User.has_permission?(user_with_roles, resource, action)
  end

  # Helper to load user roles if not already loaded
  defp load_user_roles(user) do
    case user.role_assignments do
      %Ecto.Association.NotLoaded{} ->
        UserManagement.get_user!(user.id)
      _ ->
        user
    end
  rescue
    _ -> user  # Fallback to original user if loading fails
  end
end
