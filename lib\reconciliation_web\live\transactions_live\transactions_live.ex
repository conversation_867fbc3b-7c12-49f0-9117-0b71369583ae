defmodule ReconciliationWeb.TransactionsLive do
  use ReconciliationWeb, :live_view

  alias Reconciliation.Services.{ActivityLogger, TransactionService}

  @refresh_interval 300_000 # 5 minutes (reduced from 30 seconds to prevent excessive reloading)

  def handle_event("change_page", %{"page" => page}, socket) do
    page_num = String.to_integer(page)
    {:noreply,
     socket
     |> assign(:current_page, page_num)
     |> update_paginated_transactions()
    }
  end

  @impl true
  def mount(_params, _session, socket) do
    user = socket.assigns.current_user

    # Get all transactions for the user's reconciliation runs using service
    transactions = TransactionService.get_user_transactions(user.id)
    reconciliation_runs = Reconciliation.list_reconciliation_runs(user.id)

    # Get unique dates for date filtering using service
    unique_dates = TransactionService.get_unique_transaction_dates(transactions)

    # Only start auto-refresh if there are pending reconciliation runs
    if connected?(socket) && Enum.any?(reconciliation_runs, &(&1.status in ["pending", "processing"])) do
      Process.send_after(self(), :refresh_transactions, @refresh_interval)
    end

    # Log transaction view activity
    if connected?(socket) do
      ActivityLogger.log_data_access_activity(
        user.id,
        "transaction_view",
        organization_id: user.organization_id,
        metadata: %{
          transaction_count: length(transactions),
          run_count: length(reconciliation_runs)
        }
      )
    end

    {:ok,
     socket
     |> assign(:page_title, "Transactions")
     |> assign(:transactions, transactions)
     |> assign(:all_transactions, transactions)
     |> assign(:reconciliation_runs, reconciliation_runs)
     |> assign(:unique_dates, unique_dates)
     |> assign(:selected_run_id, nil)
     |> assign(:selected_date, nil)
     |> assign(:selected_status, nil)
     |> assign(:selected_type, nil)
     |> assign(:search_query, "")
     |> assign(:current_page, 1)
     |> assign(:per_page, 12)
     |> assign(:reconciliation_in_progress, false)
     |> assign(:export_state, "idle")  # Track export button state: idle, processing, complete, error
     |> update_transaction_totals(transactions)
     |> update_paginated_transactions()
    }
  end

  @impl true
  def handle_event("filter_by_run", %{"run_id" => ""}, socket) do
    user = socket.assigns.current_user
    transactions = TransactionService.get_user_transactions(user.id)

    {:noreply,
     socket
     |> assign(:transactions, transactions)
     |> assign(:all_transactions, transactions)
     |> assign(:selected_run_id, nil)
     |> assign(:current_page, 1)
     |> update_transaction_totals(transactions)
     |> update_paginated_transactions()
    }
  end

  def handle_event("filter_by_run", %{"run_id" => run_id}, socket) do
    IO.puts("🔍 RUN FILTER: Setting run to '#{run_id}'")
    # Update the selected run first
    updated_socket = assign(socket, :selected_run_id, run_id)

    # Get base transactions for this run
    base_transactions = get_base_transactions(updated_socket)

    # Apply all filters including any existing ones
    updated_assigns = %{updated_socket.assigns | selected_run_id: run_id}
    transactions = apply_all_filters(base_transactions, updated_assigns)

    IO.puts("🔍 RUN FILTER: Base=#{length(base_transactions)}, Filtered=#{length(transactions)}")

    # Log filter activity
    if run_id && run_id != "" do
      run_name = get_run_name(socket.assigns.reconciliation_runs, run_id)
      ActivityLogger.log_data_access_activity(
        socket.assigns.current_user.id,
        "data_filter",
        organization_id: socket.assigns.current_user.organization_id,
        metadata: %{
          filter_type: "run",
          filter_value: run_name,
          results_count: length(transactions),
          page: "transactions"
        }
      )
    end

    {:noreply,
     updated_socket
     |> assign(:transactions, transactions)
     |> assign(:current_page, 1)
     |> update_transaction_totals(transactions)
     |> update_paginated_transactions()
    }
  end

  def handle_event("filter_by_date", %{"date" => ""}, socket) do
    IO.puts("🔍 DATE FILTER: Clearing date filter")
    base_transactions = get_base_transactions(socket)
    updated_assigns = %{socket.assigns | selected_date: nil}
    transactions = apply_all_filters(base_transactions, updated_assigns)
    IO.puts("🔍 DATE FILTER: Base=#{length(base_transactions)}, Filtered=#{length(transactions)}")
    {:noreply,
     socket
     |> assign(:transactions, transactions)
     |> assign(:selected_date, nil)
     |> assign(:current_page, 1)
     |> update_transaction_totals(transactions)
     |> update_paginated_transactions()
    }
  end

  def handle_event("filter_by_date", %{"date" => date}, socket) do
    IO.puts("🔍 DATE FILTER: Setting date to '#{date}'")
    base_transactions = get_base_transactions(socket)
    updated_assigns = %{socket.assigns | selected_date: date}
    transactions = apply_all_filters(base_transactions, updated_assigns)
    IO.puts("🔍 DATE FILTER: Base=#{length(base_transactions)}, Filtered=#{length(transactions)}")
    {:noreply,
     socket
     |> assign(:transactions, transactions)
     |> assign(:selected_date, date)
     |> assign(:current_page, 1)
     |> update_transaction_totals(transactions)
     |> update_paginated_transactions()
    }
  end

  def handle_event("filter_by_status", %{"status" => ""}, socket) do
    IO.puts("🔍 STATUS FILTER: Clearing status filter")
    base_transactions = get_base_transactions(socket)
    updated_assigns = %{socket.assigns | selected_status: nil}
    transactions = apply_all_filters(base_transactions, updated_assigns)
    IO.puts("🔍 STATUS FILTER: Base=#{length(base_transactions)}, Filtered=#{length(transactions)}")
    {:noreply,
     socket
     |> assign(:transactions, transactions)
     |> assign(:selected_status, nil)
     |> assign(:current_page, 1)
     |> update_transaction_totals(transactions)
     |> update_paginated_transactions()
    }
  end

  def handle_event("filter_by_status", %{"status" => status}, socket) do
    IO.puts("🔍 STATUS FILTER: Setting status to '#{status}'")
    base_transactions = get_base_transactions(socket)
    updated_assigns = %{socket.assigns | selected_status: status}
    transactions = apply_all_filters(base_transactions, updated_assigns)
    IO.puts("🔍 STATUS FILTER: Base=#{length(base_transactions)}, Filtered=#{length(transactions)}")
    {:noreply,
     socket
     |> assign(:transactions, transactions)
     |> assign(:selected_status, status)
     |> assign(:current_page, 1)
     |> update_transaction_totals(transactions)
     |> update_paginated_transactions()
    }
  end

  def handle_event("filter_by_type", %{"type" => ""}, socket) do
    IO.puts("🔍 TYPE FILTER: Clearing type filter")
    base_transactions = get_base_transactions(socket)
    updated_assigns = %{socket.assigns | selected_type: nil}
    transactions = apply_all_filters(base_transactions, updated_assigns)
    IO.puts("🔍 TYPE FILTER: Base=#{length(base_transactions)}, Filtered=#{length(transactions)}")
    {:noreply,
     socket
     |> assign(:transactions, transactions)
     |> assign(:selected_type, nil)
     |> assign(:current_page, 1)
     |> update_transaction_totals(transactions)
     |> update_paginated_transactions()
    }
  end

  def handle_event("filter_by_type", %{"type" => type}, socket) do
    IO.puts("🔍 TYPE FILTER: Setting type to '#{type}'")
    base_transactions = get_base_transactions(socket)
    updated_assigns = %{socket.assigns | selected_type: type}
    transactions = apply_all_filters(base_transactions, updated_assigns)
    IO.puts("🔍 TYPE FILTER: Base=#{length(base_transactions)}, Filtered=#{length(transactions)}")
    {:noreply,
     socket
     |> assign(:transactions, transactions)
     |> assign(:selected_type, type)
     |> assign(:current_page, 1)
     |> update_transaction_totals(transactions)
     |> update_paginated_transactions()
    }
  end

  def handle_event("clear_all_filters", _params, socket) do
    transactions = socket.assigns.all_transactions
    {:noreply,
     socket
     |> assign(:transactions, transactions)
     |> assign(:selected_run_id, nil)
     |> assign(:selected_date, nil)
     |> assign(:selected_status, nil)
     |> assign(:selected_type, nil)
     |> assign(:search_query, "")
     |> assign(:current_page, 1)
     |> update_transaction_totals(transactions)
     |> update_paginated_transactions()
    }
  end

  def handle_event("search", %{"search" => query}, socket) do
    base_transactions = get_base_transactions(socket)
    updated_assigns = %{socket.assigns | search_query: query}
    filtered_transactions = apply_all_filters(base_transactions, updated_assigns)

    # Log search activity if query is not empty
    if query && String.trim(query) != "" do
      ActivityLogger.log_data_access_activity(
        socket.assigns.current_user.id,
        "search",
        organization_id: socket.assigns.current_user.organization_id,
        metadata: %{
          search_query: query,
          results_count: length(filtered_transactions),
          page: "transactions"
        }
      )
    end

    {:noreply,
     socket
     |> assign(:transactions, filtered_transactions)
     |> assign(:search_query, query)
     |> assign(:current_page, 1)
     |> update_transaction_totals(filtered_transactions)
     |> update_paginated_transactions()
    }
  end

  def handle_event("export_transactions", %{"run_id" => run_id}, socket) do
    # Set export state to processing immediately
    socket = assign(socket, :export_state, "processing")

    try do
      run_id_int = String.to_integer(run_id)

      # Use the currently filtered transactions instead of all transactions
      # This respects the user's current filters (date, status, type, search)
      filtered_transactions = socket.assigns.transactions

      # Get the run details for filename
      run = Reconciliation.get_reconciliation_run!(run_id_int)

      # Generate Excel content using template approach
      case Reconciliation.Services.ExcelExport.generate_transactions_excel_from_template(filtered_transactions, run.name) do
        {:ok, file_path, filename} ->
          # Log the export activity
          ActivityLogger.log_reconciliation_activity(
            socket.assigns.current_user.id,
            "export_data",
            resource_type: "reconciliation_run",
            resource_id: run_id_int,
            organization_id: socket.assigns.current_user.organization_id,
            metadata: %{
              export_type: "excel",
              filename: filename,
              transaction_count: length(filtered_transactions),
              run_name: run.name
            }
          )

          # Read the file content for download
          file_content = File.read!(file_path)

          # Send download response using a more reliable method
          IO.puts("🔽 EXPORT: Sending download for file: #{filename}")
          IO.puts("🔽 EXPORT: File size: #{byte_size(file_content)} bytes")

          # Set state to complete and schedule reset to idle
          Process.send_after(self(), :reset_export_state, 2000)

          {:noreply,
           socket
           |> assign(:export_state, "complete")
           |> push_event("download", %{
             url: "data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,#{Base.encode64(file_content)}",
             filename: filename
           })
           |> put_flash(:info, "Excel exported with preserved formatting! (#{length(filtered_transactions)} transactions)")
          }

        {:error, reason} ->
          # Set state to error and schedule reset to idle
          Process.send_after(self(), :reset_export_state, 3000)

          {:noreply,
           socket
           |> assign(:export_state, "error")
           |> put_flash(:error, "Template export failed: #{reason}")
          }
      end
    rescue
      error ->
        # Set state to error and schedule reset to idle
        Process.send_after(self(), :reset_export_state, 3000)

        {:noreply,
         socket
         |> assign(:export_state, "error")
         |> put_flash(:error, "Export failed: #{Exception.message(error)}")
        }
    end
  end

  def handle_event("export_transactions", _params, socket) do
    {:noreply, put_flash(socket, :error, "Please select a reconciliation run to export.")}
  end

  # Handle export state reset
  @impl true
  def handle_info(:reset_export_state, socket) do
    {:noreply, assign(socket, :export_state, "idle")}
  end

  @impl true
  def handle_event("run_matching_engine", _params, socket) do
    run_id = socket.assigns.selected_run_id
    if run_id do
      # Set reconciliation in progress to hide the button
      socket = assign(socket, :reconciliation_in_progress, true)

      case TransactionService.run_matching_engine(run_id, socket.assigns.current_user.id) do
        {:ok, matches} ->
          # Refresh transactions with updated match status
          run_id_int = if is_binary(run_id), do: String.to_integer(run_id), else: run_id
          transactions =
            run_id_int
            |> Reconciliation.get_transactions()
            |> TransactionService.sort_transactions_by_reference_and_file()

          # Count matched vs unmatched
          matched_count = Enum.count(transactions, & &1.is_matched)
          unmatched_count = length(transactions) - matched_count

          message = "Matching completed! Found #{length(matches)} matches. #{matched_count} transactions matched, #{unmatched_count} unmatched."

          # Refresh the reconciliation runs list to reflect the updated status
          reconciliation_runs = Reconciliation.list_reconciliation_runs(socket.assigns.current_user.id)

          {:noreply,
            socket
            |> assign(:transactions, transactions)
            |> assign(:all_transactions, transactions)
            |> assign(:reconciliation_runs, reconciliation_runs)
            |> assign(:reconciliation_in_progress, false)
            |> update_transaction_totals(transactions)
            |> update_paginated_transactions()
            |> put_flash(:info, message)
          }

        {:error, error} ->
          {:noreply,
            socket
            |> assign(:reconciliation_in_progress, false)
            |> put_flash(:error, "Error running matching: #{error}")
          }
      end
    else
      {:noreply, put_flash(socket, :error, "Please select a reconciliation run first.")}
    end
  end

  @impl true
  def handle_info(:refresh_transactions, socket) do
    user = socket.assigns.current_user

    # Get fresh reconciliation runs to check if any are still pending
    fresh_runs = Reconciliation.list_reconciliation_runs(user.id)
    has_pending_runs = Enum.any?(fresh_runs, &(&1.status in ["pending", "processing"]))

    # Get fresh transactions from database
    fresh_transactions =
      if socket.assigns.selected_run_id do
        socket.assigns.selected_run_id
        |> Reconciliation.get_transactions()
        |> TransactionService.sort_transactions_by_reference_and_file()
      else
        TransactionService.get_user_transactions(user.id)
      end

    # Apply existing filters and search to preserve user's current view
    filtered_transactions = TransactionService.apply_filters(fresh_transactions, socket.assigns)

    # Only continue refreshing if there are still pending runs
    if has_pending_runs do
      Process.send_after(self(), :refresh_transactions, @refresh_interval)
    end

    {:noreply,
     socket
     |> assign(:transactions, filtered_transactions)
     |> assign(:all_transactions, fresh_transactions)
     |> assign(:reconciliation_runs, fresh_runs)
     |> update_transaction_totals(filtered_transactions)
     |> update_paginated_transactions()
    }
  end

  # Helper functions (moved to TransactionService)



  defp generate_transactions_csv(transactions) do
    # Define headers
    headers = [
      "Date",
      "Transaction ID",
      "Description",
      "Reference",
      "Amount",
      "Type",
      "Account",
      "Category",
      "Currency",
      "Matched",
      "Match Confidence",
      "Source File",
      "File Type",
      "Run Date"
    ]

    # Convert transactions to CSV rows
    rows = Enum.map(transactions, fn transaction ->
      [
        format_date_for_export(transaction.transaction_date),
        transaction.transaction_id || "",
        transaction.description || "",
        transaction.reference || "",
        Decimal.to_string(transaction.amount),
        transaction.transaction_type || "",
        transaction.account || "",
        transaction.category || "",
        transaction.currency || "USD",
        if(transaction.is_matched, do: "Yes", else: "No"),
        if(transaction.match_confidence, do: Decimal.to_string(transaction.match_confidence), else: ""),
        if(transaction.uploaded_file, do: transaction.uploaded_file.filename, else: ""),
        if(transaction.uploaded_file, do: transaction.uploaded_file.file_type, else: ""),
        format_date_for_export(transaction.inserted_at)
      ]
    end)

    # Generate CSV content using built-in Elixir functionality
    [headers | rows]
    |> Enum.map(&format_csv_row/1)
    |> Enum.join("\n")
  end

  # Helper function to format a CSV row with proper escaping
  defp format_csv_row(row) do
    row
    |> Enum.map(&escape_csv_field/1)
    |> Enum.join(",")
  end

  # Helper function to escape CSV fields
  defp escape_csv_field(field) when is_binary(field) do
    if String.contains?(field, [",", "\"", "\n", "\r"]) do
      "\"#{String.replace(field, "\"", "\"\"")}\""
    else
      field
    end
  end
  defp escape_csv_field(field), do: to_string(field)

  defp format_date_for_export(nil), do: ""
  defp format_date_for_export(%Date{} = date), do: Date.to_string(date)
  defp format_date_for_export(%DateTime{} = datetime), do: DateTime.to_date(datetime) |> Date.to_string()
  defp format_date_for_export(%NaiveDateTime{} = naive_datetime), do: NaiveDateTime.to_date(naive_datetime) |> Date.to_string()

  defp update_paginated_transactions(socket) do
    # Use filtered transactions for pagination, not all_transactions
    filtered_transactions = socket.assigns.transactions
    current_page = socket.assigns.current_page
    per_page = socket.assigns.per_page

    total_pages = ceil(length(filtered_transactions) / per_page)
    start_index = (current_page - 1) * per_page

    paginated_transactions =
      filtered_transactions
      |> Enum.drop(start_index)
      |> Enum.take(per_page)

    socket
    |> assign(:paginated_transactions, paginated_transactions)
    |> assign(:total_pages, total_pages)
    |> assign(:total_records, length(filtered_transactions))
  end

  # Function moved to TransactionService

  defp filter_transactions(transactions, ""), do: transactions
  defp filter_transactions(transactions, query) do
    query_lower = String.downcase(query)

    Enum.filter(transactions, fn transaction ->
      String.contains?(String.downcase(transaction.description || ""), query_lower) ||
      String.contains?(String.downcase(transaction.reference || ""), query_lower) ||
      String.contains?(String.downcase(to_string(transaction.amount)), query_lower)
    end)
  end

  defp update_transaction_totals(socket, transactions) do
    {total_debits, total_credits} =
      Enum.reduce(transactions, {Decimal.new(0), Decimal.new(0)}, fn transaction, {debits_acc, credits_acc} ->
        cond do
          Decimal.negative?(transaction.amount) ->
            {Decimal.add(debits_acc, Decimal.abs(transaction.amount)), credits_acc}
          Decimal.positive?(transaction.amount) ->
            {debits_acc, Decimal.add(credits_acc, transaction.amount)}
          true ->
            {debits_acc, credits_acc}
        end
      end)

    # Calculate currency-based totals
    currency_totals = calculate_currency_totals(transactions)

    # Calculate file source totals
    file_source_totals = calculate_file_source_totals(transactions)

    socket
    |> assign(:total_debits, total_debits)
    |> assign(:total_credits, total_credits)
    |> assign(:currency_totals, currency_totals)
    |> assign(:file_source_totals, file_source_totals)
  end

  defp calculate_currency_totals(transactions) do
    transactions
    |> Enum.group_by(fn transaction ->
      transaction.currency || "USD" # Default to USD if no currency
    end)
    |> Enum.map(fn {currency, currency_transactions} ->
      {debit_count, debit_total, credit_count, credit_total} =
        Enum.reduce(currency_transactions, {0, Decimal.new(0), 0, Decimal.new(0)},
          fn transaction, {debit_count_acc, debit_total_acc, credit_count_acc, credit_total_acc} ->
            cond do
              Decimal.negative?(transaction.amount) ->
                {debit_count_acc + 1,
                 Decimal.add(debit_total_acc, Decimal.abs(transaction.amount)),
                 credit_count_acc,
                 credit_total_acc}
              Decimal.positive?(transaction.amount) ->
                {debit_count_acc,
                 debit_total_acc,
                 credit_count_acc + 1,
                 Decimal.add(credit_total_acc, transaction.amount)}
              true ->
                {debit_count_acc, debit_total_acc, credit_count_acc, credit_total_acc}
            end
          end)

      {currency, %{
        debit_count: debit_count,
        debit_total: debit_total,
        credit_count: credit_count,
        credit_total: credit_total
      }}
    end)
    |> Enum.into(%{})
  end

  defp calculate_file_source_totals(transactions) do
    transactions
    |> Enum.group_by(fn transaction ->
      case transaction.uploaded_file do
        %{file_type: "file_a"} = uploaded_file ->
          Reconciliation.UploadedFile.short_display_name(uploaded_file)
        %{file_type: "file_b"} = uploaded_file ->
          Reconciliation.UploadedFile.short_display_name(uploaded_file)
        _ -> "Unknown"
      end
    end)
    |> Enum.map(fn {file_source, file_transactions} ->
      {debit_count, debit_total, credit_count, credit_total} =
        Enum.reduce(file_transactions, {0, Decimal.new(0), 0, Decimal.new(0)},
          fn transaction, {debit_count_acc, debit_total_acc, credit_count_acc, credit_total_acc} ->
            cond do
              Decimal.negative?(transaction.amount) ->
                {debit_count_acc + 1,
                 Decimal.add(debit_total_acc, Decimal.abs(transaction.amount)),
                 credit_count_acc,
                 credit_total_acc}
              Decimal.positive?(transaction.amount) ->
                {debit_count_acc,
                 debit_total_acc,
                 credit_count_acc + 1,
                 Decimal.add(credit_total_acc, transaction.amount)}
              true ->
                {debit_count_acc, debit_total_acc, credit_count_acc, credit_total_acc}
            end
          end)

      {file_source, %{
        debit_count: debit_count,
        debit_total: debit_total,
        credit_count: credit_count,
        credit_total: credit_total
      }}
    end)
    |> Enum.into(%{})
  end

  defp format_currency(amount) when is_nil(amount), do: "0.00"
  defp format_currency(amount) do
    Decimal.to_string(amount, :normal)
  end

  defp format_currency_with_symbol(amount, currency) when is_nil(amount), do: "#{currency_symbol(currency)}0.00"
  defp format_currency_with_symbol(amount, currency) do
    "#{currency_symbol(currency)}#{Decimal.to_string(amount, :normal)}"
  end

  defp format_currency_with_sign(amount) when is_nil(amount), do: "0.00"
  defp format_currency_with_sign(amount) do
    if Decimal.negative?(amount) do
      "-#{Decimal.to_string(Decimal.abs(amount), :normal)}"
    else
      "+#{Decimal.to_string(amount, :normal)}"
    end
  end

  defp currency_symbol(currency) when is_binary(currency) do
    case String.upcase(currency) do
      "USD" -> "$"
      "EUR" -> "€"
      "GBP" -> "£"
      "JPY" -> "¥"
      "CAD" -> "C$"
      "AUD" -> "A$"
      "MWK" -> "MK "
      "ZAR" -> "R "
      "KES" -> "KSh "
      "TZS" -> "TSh "
      "UGX" -> "USh "
      "ZMW" -> "ZK "
      "BWP" -> "P "
      "NAD" -> "N$ "
      "SZL" -> "L "
      "LSL" -> "M "
      other -> "#{other} "
    end
  end
  defp currency_symbol(_), do: ""

  defp format_date(nil), do: "-"
  defp format_date(date), do: Calendar.strftime(date, "%b %d, %Y")

  defp match_status_badge(true) do
    assigns = %{}
    ~H"""
    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
      Matched
    </span>
    """
  end

  defp match_status_badge(false) do
    assigns = %{}
    ~H"""
    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
      Unmatched
    </span>
    """
  end

  defp file_type_badge("file_a") do
    assigns = %{}
    ~H"""
    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
      File A
    </span>
    """
  end

  defp file_type_badge("file_b") do
    assigns = %{}
    ~H"""
    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
      File B
    </span>
    """
  end

  defp file_type_badge(_) do
    assigns = %{}
    ~H"""
    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
      Unknown
    </span>
    """
  end

  defp transaction_type_badge("debit") do
    assigns = %{}
    ~H"""
    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
      Debit
    </span>
    """
  end

  defp transaction_type_badge("credit") do
    assigns = %{}
    ~H"""
    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
      Credit
    </span>
    """
  end

  defp transaction_type_badge("transfer") do
    assigns = %{}
    ~H"""
    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
      Transfer
    </span>
    """
  end

  defp transaction_type_badge("fee") do
    assigns = %{}
    ~H"""
    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800">
      Fee
    </span>
    """
  end

  defp transaction_type_badge("interest") do
    assigns = %{}
    ~H"""
    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">
      Interest
    </span>
    """
  end

  defp transaction_type_badge("other") do
    assigns = %{}
    ~H"""
    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-indigo-100 text-indigo-800">
      Other
    </span>
    """
  end

  defp transaction_type_badge(nil) do
    assigns = %{}
    ~H"""
    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-500">
      N/A
    </span>
    """
  end

  defp transaction_type_badge(type) when is_binary(type) do
    assigns = %{type: String.capitalize(type)}
    ~H"""
    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
      <%= @type %>
    </span>
    """
  end

  defp transaction_type_badge(_) do
    assigns = %{}
    ~H"""
    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-500">
      Unknown
    </span>
    """
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <h2 class="text-xl font-bold mb-4 text-white">Transactions</h2>
      <!-- Enhanced Filtering Section -->
      <div class="mb-6 bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">Filter Transactions</h3>

        <!-- Filter Controls Row -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
          <!-- Date Filter -->
          <div class="space-y-2">
            <label for="date_filter" class="block text-sm font-medium text-gray-700">Date</label>
            <form phx-change="filter_by_date">
              <select name="date" id="date_filter" class="w-full border border-gray-300 rounded-md px-3 py-2 bg-white text-gray-900 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                <option value="">All Dates</option>
                <%= for date <- @unique_dates do %>
                  <option value={date} selected={@selected_date == date}><%= format_filter_date(date) %></option>
                <% end %>
              </select>
            </form>
          </div>

          <!-- Status Filter -->
          <div class="space-y-2">
            <label for="status_filter" class="block text-sm font-medium text-gray-700">Status</label>
            <form phx-change="filter_by_status">
              <select name="status" id="status_filter" class="w-full border border-gray-300 rounded-md px-3 py-2 bg-white text-gray-900 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                <option value="">All Status</option>
                <option value="matched" selected={@selected_status == "matched"}>Matched</option>
                <option value="unmatched" selected={@selected_status == "unmatched"}>Unmatched</option>
              </select>
            </form>
          </div>

          <!-- Type Filter -->
          <div class="space-y-2">
            <label for="type_filter" class="block text-sm font-medium text-gray-700">Type</label>
            <form phx-change="filter_by_type">
              <select name="type" id="type_filter" class="w-full border border-gray-300 rounded-md px-3 py-2 bg-white text-gray-900 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                <option value="">All Types</option>
                <option value="debit" selected={@selected_type == "debit"}>Debit</option>
                <option value="credit" selected={@selected_type == "credit"}>Credit</option>
                <option value="transfer" selected={@selected_type == "transfer"}>Transfer</option>
                <option value="fee" selected={@selected_type == "fee"}>Fee</option>
                <option value="interest" selected={@selected_type == "interest"}>Interest</option>
              </select>
            </form>
          </div>

          <!-- Run Filter -->
          <div class="space-y-2">
            <label for="run_id" class="block text-sm font-medium text-gray-700">Reconciliation Run</label>
            <form phx-change="filter_by_run">
              <select name="run_id" id="run_id" class="w-full border border-gray-300 rounded-md px-3 py-2 bg-white text-gray-900 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                <option value="">All Runs</option>
                <%= for run <- @reconciliation_runs do %>
                  <%
                    # Determine status indicator and styling
                    {status_icon, status_text, option_class} = case run.status do
                      "pending" -> {"🟡", "PENDING", "background-color: #fef3c7; font-weight: bold;"}
                      "completed" -> {"✅", "COMPLETED", ""}
                      "processing" -> {"🔄", "PROCESSING", "background-color: #dbeafe;"}
                      "failed" -> {"❌", "FAILED", "background-color: #fee2e2;"}
                      _ -> {"⚪", "UNKNOWN", ""}
                    end
                  %>
                  <option
                    value={run.id}
                    selected={@selected_run_id == to_string(run.id)}
                    style={option_class}
                  >
                    <%= status_icon %> <%= run.name %> - <%= status_text %>
                  </option>
                <% end %>
              </select>
            </form>
          </div>
        </div>

        <!-- Search and Actions Row -->
        <div class="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
          <!-- Search -->
          <div class="flex-1">
            <form phx-submit="search" class="flex gap-2">
              <div class="flex-1">
                <input
                  name="search"
                  id="search_input"
                  value={@search_query}
                  placeholder="Search by description, reference, transaction ID, or amount..."
                  class="w-full border border-gray-300 rounded-md px-3 py-2 bg-white text-gray-900 placeholder-gray-500 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm font-medium">
                Search
              </button>
            </form>
          </div>

          <!-- Clear All Filters Button -->
          <button
            phx-click="clear_all_filters"
            class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors text-sm font-medium"
          >
            Clear All Filters
          </button>
        </div>

        <!-- Active Filters Display -->
        <%= if @selected_date || @selected_status || @selected_type || @selected_run_id || (@search_query && @search_query != "") do %>
          <div class="mt-4 pt-4 border-t border-gray-200">
            <div class="flex flex-wrap items-center gap-2">
              <span class="text-sm font-medium text-gray-700">Active filters:</span>
              <%= if @selected_date do %>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  Date: <%= format_filter_date(@selected_date) %>
                </span>
              <% end %>
              <%= if @selected_status do %>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Status: <%= String.capitalize(@selected_status) %>
                </span>
              <% end %>
              <%= if @selected_type do %>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                  Type: <%= String.capitalize(@selected_type) %>
                </span>
              <% end %>
              <%= if @selected_run_id do %>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                  Run: <%= get_run_name(@reconciliation_runs, @selected_run_id) %>
                </span>
              <% end %>
              <%= if @search_query && @search_query != "" do %>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                  Search: "<%= @search_query %>"
                </span>
              <% end %>
            </div>
          </div>
        <% end %>
      </div>

      <div>
      <%= if @selected_run_id do %>
          <%
            # Get the selected run to check its status
            selected_run = Enum.find(@reconciliation_runs, &(to_string(&1.id) == @selected_run_id))
          %>
          <%= if selected_run && selected_run.status == "completed" do %>
            <!-- Show Export button when reconciliation is completed -->
            <%= case @export_state do %>
              <% "processing" -> %>
                <button disabled class="ml-2 px-4 py-1 bg-blue-500 text-white rounded cursor-not-allowed flex items-center gap-2">
                  <svg class="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Exporting...
                </button>
              <% "complete" -> %>
                <button disabled class="ml-2 px-4 py-1 bg-green-600 text-white rounded cursor-not-allowed flex items-center gap-2">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Export Complete!
                </button>
              <% "error" -> %>
                <button disabled class="ml-2 px-4 py-1 bg-red-500 text-white rounded cursor-not-allowed flex items-center gap-2">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                  </svg>
                  Export Failed
                </button>
              <% _ -> %>
                <button
                  phx-click="export_transactions"
                  phx-value-run_id={@selected_run_id}
                  class="ml-2 px-4 py-1 bg-green-500 text-white rounded hover:bg-green-600 transition-colors duration-200 flex items-center gap-2"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                  Export to Excel
                </button>
            <% end %>
          <% else %>
            <!-- Show reconciliation controls for all runs -->
            <%= if selected_run do %>
              <%= if @reconciliation_in_progress do %>
                <!-- Show processing state when reconciliation is in progress -->
                <button disabled class="ml-4 px-3 py-1 bg-gray-400 text-white rounded cursor-not-allowed flex items-center gap-2">
                  <svg class="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Processing...
                </button>
              <% else %>
                <!-- Show Start/Re-run Reconciliation button for all runs -->
                <%= if selected_run.status == "pending" do %>
                  <button phx-click="run_matching_engine" class="ml-4 px-3 py-1 bg-green-400 text-white rounded hover:bg-green-500 transition">Start Reconciliation</button>
                <% else %>
                  <button phx-click="run_matching_engine" class="ml-4 px-3 py-1 bg-blue-400 text-white rounded hover:bg-blue-500 transition">Re-run Reconciliation</button>
                <% end %>
              <% end %>
            <% end %>
          <% end %>
        <% end %>
      </div>
      <div class="overflow-x-auto">
        <table class="min-w-full bg-gray-800 border border-gray-600 table-fixed rounded-lg">
        <thead>
          <tr>
            <th class="py-2 px-4 border-b border-gray-600 text-left text-sm font-semibold text-gray-300 w-28 min-w-28">Date</th>
            <th class="py-2 px-4 border-b border-gray-600 text-left text-sm font-semibold text-gray-300 min-w-48">Description</th>
            <th class="py-2 px-4 border-b border-gray-600 text-left text-sm font-semibold text-gray-300 w-28 min-w-28">Reference</th>
            <th class="py-2 px-4 border-b border-gray-600 text-left text-sm font-semibold text-gray-300 w-28 min-w-28">Amount</th>
            <th class="py-2 px-4 border-b border-gray-600 text-left text-sm font-semibold text-gray-300 w-20 min-w-20">Type</th>
            <th class="py-2 px-4 border-b border-gray-600 text-left text-sm font-semibold text-gray-300 w-24 min-w-24">Matched</th>
            <th class="py-2 px-4 border-b border-gray-600 text-left text-sm font-semibold text-gray-300 w-32 min-w-32">Source File</th>
          </tr>
        </thead>
        <tbody class="text-sm divide-y divide-gray-600">
          <%= for transaction <- @paginated_transactions do %>
            <tr>
              <td class="py-2 px-4 border-b border-gray-600 text-white w-28 min-w-28 whitespace-nowrap"><%= format_date(transaction.inserted_at) %></td>
              <td class="py-2 px-4 border-b border-gray-600 text-white min-w-48 break-words"><%= transaction.description %></td>
              <td class="py-2 px-4 border-b border-gray-600 text-white w-28 min-w-28 break-words"><%= transaction.reference %></td>
              <td class="py-2 px-4 border-b border-gray-600 text-white w-28 min-w-28 text-right whitespace-nowrap"><%= format_currency(transaction.amount) %></td>
              <td class="py-2 px-4 border-b border-gray-600 w-20 min-w-20"><%= transaction_type_badge(transaction.transaction_type) %></td>
              <td class="py-2 px-4 border-b border-gray-600 w-24 min-w-24"><%= match_status_badge(transaction.is_matched) %></td>
              <td class="py-2 px-4 border-b border-gray-600 w-32 min-w-32">
                <%= if transaction.uploaded_file do %>
                  <div class="flex items-center">
                    <%= file_type_badge(transaction.uploaded_file.file_type) %>
                    <span class="ml-2 text-sm text-gray-300 truncate" title={Reconciliation.UploadedFile.short_display_name(transaction.uploaded_file)}>
                      <%= Reconciliation.UploadedFile.short_display_name(transaction.uploaded_file) %>
                    </span>
                  </div>
                <% else %>
                  <span class="text-gray-400">Unknown</span>
                <% end %>
              </td>
            </tr>
          <% end %>
        </tbody>
        </table>
      </div>

      <!-- Pagination Controls -->
      <div class="mt-4 flex items-center justify-between">
        <div class="text-sm text-gray-300">
          Showing <%= (@current_page - 1) * @per_page + 1 %> to <%= min(@current_page * @per_page, @total_records) %> of <%= @total_records %> transactions
        </div>

        <div class="flex items-center space-x-2">
          <%= if @current_page > 1 do %>
            <button phx-click="change_page" phx-value-page={@current_page - 1} class="px-3 py-1 bg-gray-600 text-white rounded hover:bg-gray-500 transition">
              Previous
            </button>
          <% end %>

          <%= for page <- max(1, @current_page - 2)..min(@total_pages, @current_page + 2) do %>
            <button
              phx-click="change_page"
              phx-value-page={page}
              class={"px-3 py-1 rounded transition " <> if page == @current_page, do: "bg-orange-600 text-white", else: "bg-gray-600 text-white hover:bg-gray-500"}
            >
              <%= page %>
            </button>
          <% end %>

          <%= if @current_page < @total_pages do %>
            <button phx-click="change_page" phx-value-page={@current_page + 1} class="px-3 py-1 bg-gray-600 text-white rounded hover:bg-gray-500 transition">
              Next
            </button>
          <% end %>
        </div>
      </div>
    </div>
    """
  end

  # Helper functions for improved filtering

  defp get_unique_transaction_dates(transactions) do
    transactions
    |> Enum.map(fn transaction ->
      case transaction.inserted_at do
        %DateTime{} = dt -> Date.to_string(DateTime.to_date(dt))
        %Date{} = date -> Date.to_string(date)
        _ -> nil
      end
    end)
    |> Enum.reject(&is_nil/1)
    |> Enum.uniq()
    |> Enum.sort(:desc)
  end

  defp get_base_transactions(socket) do
    if socket.assigns.selected_run_id do
      run_id_int = String.to_integer(socket.assigns.selected_run_id)
      run_id_int
      |> Reconciliation.get_transactions()
      |> TransactionService.sort_transactions_by_reference_and_file()
    else
      socket.assigns.all_transactions
    end
  end

  defp apply_all_filters(transactions, assigns) do
    transactions
    |> filter_by_date(assigns[:selected_date])
    |> filter_by_status(assigns[:selected_status])
    |> filter_by_type(assigns[:selected_type])
    |> filter_by_search(assigns[:search_query])
  end

  defp filter_by_date(transactions, nil) do
    IO.puts("🔍 FILTER_BY_DATE: No date filter, passing through #{length(transactions)} transactions")
    transactions
  end
  defp filter_by_date(transactions, "") do
    IO.puts("🔍 FILTER_BY_DATE: Empty date filter, passing through #{length(transactions)} transactions")
    transactions
  end
  defp filter_by_date(transactions, date_string) do
    IO.puts("🔍 FILTER_BY_DATE: Filtering #{length(transactions)} transactions by date '#{date_string}'")
    {:ok, target_date} = Date.from_iso8601(date_string)

    result = Enum.filter(transactions, fn transaction ->
      transaction_date = case transaction.inserted_at do
        %DateTime{} = dt -> DateTime.to_date(dt)
        %Date{} = date -> date
        _ -> nil
      end

      transaction_date == target_date
    end)

    IO.puts("🔍 FILTER_BY_DATE: Result: #{length(result)} transactions")
    result
  end

  defp filter_by_status(transactions, nil) do
    IO.puts("🔍 FILTER_BY_STATUS: No status filter, passing through #{length(transactions)} transactions")
    transactions
  end
  defp filter_by_status(transactions, "") do
    IO.puts("🔍 FILTER_BY_STATUS: Empty status filter, passing through #{length(transactions)} transactions")
    transactions
  end
  defp filter_by_status(transactions, "matched") do
    IO.puts("🔍 FILTER_BY_STATUS: Filtering #{length(transactions)} transactions by status 'matched'")
    result = Enum.filter(transactions, & &1.is_matched)
    IO.puts("🔍 FILTER_BY_STATUS: Result: #{length(result)} transactions")
    result
  end
  defp filter_by_status(transactions, "unmatched") do
    IO.puts("🔍 FILTER_BY_STATUS: Filtering #{length(transactions)} transactions by status 'unmatched'")
    result = Enum.filter(transactions, &(not &1.is_matched))
    IO.puts("🔍 FILTER_BY_STATUS: Result: #{length(result)} transactions")
    result
  end

  defp filter_by_type(transactions, nil) do
    IO.puts("🔍 FILTER_BY_TYPE: No type filter, passing through #{length(transactions)} transactions")
    transactions
  end
  defp filter_by_type(transactions, "") do
    IO.puts("🔍 FILTER_BY_TYPE: Empty type filter, passing through #{length(transactions)} transactions")
    transactions
  end
  defp filter_by_type(transactions, type) do
    IO.puts("🔍 FILTER_BY_TYPE: Filtering #{length(transactions)} transactions by type '#{type}'")
    result = Enum.filter(transactions, fn transaction ->
      String.downcase(transaction.transaction_type || "") == String.downcase(type)
    end)
    IO.puts("🔍 FILTER_BY_TYPE: Result: #{length(result)} transactions")
    result
  end

  defp filter_by_search(transactions, nil), do: transactions
  defp filter_by_search(transactions, ""), do: transactions
  defp filter_by_search(transactions, query) do
    IO.puts("🔍 FILTER_BY_SEARCH: Query = '#{query}', Input count = #{length(transactions)}")
    query_lower = String.downcase(query)

    result = Enum.filter(transactions, fn transaction ->
      desc_match = String.contains?(String.downcase(transaction.description || ""), query_lower)
      ref_match = String.contains?(String.downcase(transaction.reference || ""), query_lower)
      txn_id_match = String.contains?(String.downcase(transaction.transaction_id || ""), query_lower)
      amount_match = String.contains?(String.downcase(to_string(transaction.amount)), query_lower)

      matches = desc_match || ref_match || txn_id_match || amount_match

      # Debug logging for matches
      if matches do
        IO.puts("✅ MATCH: '#{transaction.description}' (desc:#{desc_match}, ref:#{ref_match}, txn:#{txn_id_match}, amt:#{amount_match})")
      end

      matches
    end)

    IO.puts("🔍 FILTER_BY_SEARCH: Output count = #{length(result)}")
    result
  end

  defp format_filter_date(%Date{} = date) do
    Calendar.strftime(date, "%b %d, %Y")
  end

  defp format_filter_date(date_string) when is_binary(date_string) do
    case Date.from_iso8601(date_string) do
      {:ok, date} -> Calendar.strftime(date, "%b %d, %Y")
      _ -> date_string
    end
  end

  defp format_filter_date(nil), do: ""
  defp format_filter_date(other), do: to_string(other)

  defp get_run_name(reconciliation_runs, run_id) do
    case Enum.find(reconciliation_runs, &(to_string(&1.id) == run_id)) do
      nil -> "Unknown"
      run -> run.name
    end
  end

  # Helper function to build filter information for filename
  defp build_filter_info(assigns) do
    filters = []

    filters = if assigns[:selected_date] && assigns[:selected_date] != "",
      do: ["date_#{assigns[:selected_date]}" | filters],
      else: filters

    filters = if assigns[:selected_status] && assigns[:selected_status] != "",
      do: ["#{assigns[:selected_status]}" | filters],
      else: filters

    filters = if assigns[:selected_type] && assigns[:selected_type] != "",
      do: ["#{assigns[:selected_type]}" | filters],
      else: filters

    filters = if assigns[:search_query] && assigns[:search_query] != "",
      do: ["search" | filters],
      else: filters

    if length(filters) > 0 do
      "_filtered_" <> Enum.join(filters, "_")
    else
      ""
    end
  end
end
