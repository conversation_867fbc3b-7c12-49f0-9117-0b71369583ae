<aside class="w-64 flex flex-col h-full shadow-sm" style="background: rgba(249, 250, 251, 0.98); border-right: 1px solid rgba(229, 231, 235, 0.8);">
  <!-- Logo Section -->
  <div class="px-6 py-8 border-b border-gray-200">
    <div class="flex items-center group cursor-pointer">
      <div class="w-32 h-32 flex items-center justify-center transition-transform duration-200 group-hover:scale-105">
        <img src={~p"/images/probase-logo.png"} alt="ProBASE Logo" class="w-full h-full object-contain" />
      </div>

    </div>
  </div>

  <!-- Navigation -->
  <nav class="flex-1 px-4 py-6 space-y-2">
    <div class="space-y-1">
      <!-- Dashboard -->
      <a href={~p"/dashboard"} class={nav_class(@current_path || "/", "/dashboard")}>
        <.icon name="hero-home" class={icon_class(@current_path || "/", "/dashboard")} />
        <span>Dashboard</span>
        <div class={active_indicator_class(@current_path || "/", "/dashboard")}></div>
      </a>

      <!-- Summary -->
      <a href={~p"/summary"} class={nav_class(@current_path || "/", "/summary")}>
        <.icon name="hero-chart-pie" class={icon_class(@current_path || "/", "/summary")} />
        <span>Summary</span>
        <div class={active_indicator_class(@current_path || "/", "/summary")}></div>
      </a>

      <!-- Reconciliation -->
      <a href={~p"/reconciliation"} class={nav_class(@current_path || "/", "/reconciliation")}>
        <.icon name="hero-document-check" class={icon_class(@current_path || "/", "/reconciliation")} />
        <span>Reconciliation</span>
        <div class={active_indicator_class(@current_path || "/", "/reconciliation")}></div>
      </a>

      <!-- Transactions -->
      <a href={~p"/transactions"} class={nav_class(@current_path || "/", "/transactions")}>
        <.icon name="hero-banknotes" class={icon_class(@current_path || "/", "/transactions")} />
        <span class="flex items-center justify-between w-full">
          <span>Transactions</span>
          <%= if @pending_runs_count > 0 do %>
            <span class="bg-red-500 text-white text-xs rounded-full px-2 py-0.5 ml-2 font-semibold">
              <%= @pending_runs_count %>
            </span>
          <% end %>
        </span>
        <div class={active_indicator_class(@current_path || "/", "/transactions")}></div>
      </a>

      <!-- Reports -->
      <a href={~p"/reports"} class={nav_class(@current_path || "/", "/reports")}>
        <.icon name="hero-chart-bar" class={icon_class(@current_path || "/", "/reports")} />
        <span>Reports</span>
        <div class={active_indicator_class(@current_path || "/", "/reports")}></div>
      </a>


    </div>

    <!-- User Management Section (Admin/Manager only) -->
    <%= if @current_user && user_can_manage_users?(assigns) do %>
      <div class="border-t border-gray-200 pt-4 mt-6">
        <div class="px-3 mb-2">
          <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider">User Management</h3>
        </div>
        <div class="space-y-1">
          <!-- Users -->
          <a href={~p"/admin/users"} class={nav_class(@current_path || "/", "/admin/users", "probase")}>
            <.icon name="hero-users" class={icon_class(@current_path || "/", "/admin/users", "probase")} />
            <span>Users</span>
            <div class={active_indicator_class(@current_path || "/", "/admin/users", "probase")}></div>
          </a>

          <!-- Roles & Permissions (Admin only) -->
          <%= if @current_user && user_has_role?(assigns, "admin") do %>
            <a href={~p"/admin/roles"} class={nav_class(@current_path || "/", "/admin/roles", "probase")}>
              <.icon name="hero-shield-check" class={icon_class(@current_path || "/", "/admin/roles", "probase")} />
              <span>Roles & Permissions</span>
              <div class={active_indicator_class(@current_path || "/", "/admin/roles", "probase")}></div>
            </a>
          <% end %>

          <!-- Activity Log -->
          <a href={~p"/activity"} class={nav_class(@current_path || "/", "/activity", "probase")}>
            <.icon name="hero-clipboard-document-list" class={icon_class(@current_path || "/", "/activity", "probase")} />
            <span>Activity Log</span>
            <div class={active_indicator_class(@current_path || "/", "/activity", "probase")}></div>
          </a>

          <!-- Organizations (Admin only) - HIDDEN -->
          <%!-- <%= if @current_user && user_has_role?(@current_user, "admin") do %>
            <a href={~p"/admin/organizations"} class={nav_class(@current_path || "/", "/admin/organizations", "probase")}>
              <.icon name="hero-building-office" class={icon_class(@current_path || "/", "/admin/organizations", "probase")} />
              <span>Organizations</span>
              <div class={active_indicator_class(@current_path || "/", "/admin/organizations", "probase")}></div>
            </a>
          <% end %> --%>

          <!-- Teams - HIDDEN -->
          <%!-- <a href={~p"/admin/teams"} class={nav_class(@current_path || "/", "/admin/teams", "probase")}>
            <.icon name="hero-user-group" class={icon_class(@current_path || "/", "/admin/teams", "probase")} />
            <span>Teams</span>
            <div class={active_indicator_class(@current_path || "/", "/admin/teams", "probase")}></div>
          </a> --%>


        </div>
      </div>
    <% end %>

    <!-- Divider -->
    <div class="border-t border-gray-200 pt-4 mt-6">
      <div class="space-y-1">
        <!-- Settings -->
        <a href={~p"/settings"} class={nav_class(@current_path || "/", "/settings", "probase")}>
          <.icon name="hero-cog-6-tooth" class={icon_class(@current_path || "/", "/settings", "probase")} />
          <span>Settings</span>
          <div class={active_indicator_class(@current_path || "/", "/settings", "probase")}></div>
        </a>
      </div>
    </div>
  </nav>

  <!-- Logout Section -->
  <div class="px-4 py-4 border-t border-gray-200">
    <.form action={~p"/users/log_out"} method="delete" class="w-full">
      <button type="submit"
              class="w-full group flex items-center px-3 py-2.5 text-sm font-medium rounded-xl transition-all duration-200 hover:bg-red-50 hover:text-red-700 text-gray-700 hover:shadow-sm">
        <.icon name="hero-arrow-left-on-rectangle" class="w-5 h-5 mr-3 text-gray-400 group-hover:text-red-600" />
        <span>Sign Out</span>
        <div class="ml-auto w-2 h-2 bg-red-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"></div>
      </button>
    </.form>

    <!-- Version Info -->
    <div class="mt-4 px-3 py-2 text-center">
      <p class="text-xs text-gray-500 font-medium">v1.0.0</p>
      <p class="text-xs text-gray-600">© 2025 ProBASE</p>
    </div>
  </div>
</aside>