# defmodule Reconciliation.Services.ExcelizerExport do
#   @moduledoc """
#   Excel export service using Excelizer library for template-based exports.
#   Excelizer provides native Excel file manipulation with full formatting preservation.
#   """

#   require Logger

#   @template_filename "transactions_Reconciliation.xlsx"
#   @completed_folder "completed"

#   @doc """
#   Generate Excel content using Excelizer with template preservation.
#   This is the main export function that uses Excelizer to populate the template
#   while preserving all formatting, conditional formatting, and styles.
#   """
#   def generate_transactions_excel_from_template(transactions, run_name \\ "export") do
#     try do
#       # Get the project root directory
#       project_root = File.cwd!()
#       template_path = Path.join(project_root, @template_filename)

#       # Ensure completed folder exists
#       completed_dir = Path.join(project_root, @completed_folder)
#       File.mkdir_p!(completed_dir)

#       # Generate unique filename for the completed export
#       timestamp = DateTime.utc_now() |> DateTime.to_iso8601(:basic) |> String.replace(":", "-")
#       completed_filename = "transactions_#{run_name}_#{timestamp}_excelizer.xlsx"
#       completed_path = Path.join(completed_dir, completed_filename)

#       # Try template approach first, fallback to new workbook if template fails
#       result = if File.exists?(template_path) do
#         Logger.info("Using template approach with: #{template_path}")
#         populate_template_with_excelizer(template_path, transactions, completed_path)
#       else
#         Logger.warn("Template file not found: #{template_path}, creating new workbook")
#         create_new_workbook_with_excelizer(transactions, completed_path)
#       end

#       case result do
#         {:ok, _} ->
#           Logger.info("Successfully created Excel file with #{length(transactions)} transactions using Excelizer")
#           {:ok, completed_path, completed_filename}

#         {:error, reason} ->
#           Logger.error("Failed to create Excel file with Excelizer: #{reason}")
#           {:error, reason}
#       end

#     rescue
#       error ->
#         Logger.error("Failed to generate Excel from template with Excelizer: #{inspect(error)}")
#         {:error, Exception.message(error)}
#     end
#   end

#   @doc """
#   Populate Excel template using Excelizer library.
#   This function loads the template, populates it with transaction data,
#   and saves it while preserving all formatting.
#   """
#   defp populate_template_with_excelizer(template_path, transactions, output_path) do
#     try do
#       Logger.info("Loading template with Excelizer: #{template_path}")

#       # Use Excelizer to open the template file and process it
#       case Excelizer.open(template_path, fn file_id ->
#         Logger.info("Template loaded successfully with file_id: #{inspect(file_id)}")

#         # Populate the sheet with transaction data (assume Sheet1 exists)
#         populate_transaction_data_excelizer(file_id, transactions)

#         # Save the file to the output path using the correct API
#         case Excelizer.Native.Base.save_as(file_id, output_path) do
#           {:ok, _} ->
#             Logger.info("File saved successfully to: #{output_path}")
#             :ok
#           {:error, reason} ->
#             Logger.error("Failed to save file: #{inspect(reason)}")
#             {:error, "Failed to save file: #{inspect(reason)}"}
#         end
#       end) do
#         :ok ->
#           Logger.info("Successfully populated template with Excelizer: #{output_path}")
#           {:ok, output_path}

#         {:error, reason} ->
#           Logger.error("Failed to process template with Excelizer: #{inspect(reason)}")
#           {:error, "Failed to process template: #{inspect(reason)}"}
#       end

#     rescue
#       error ->
#         Logger.error("Failed to populate template with Excelizer: #{inspect(error)}")
#         {:error, Exception.message(error)}
#     end
#   end

#   @doc """
#   Populate worksheet with transaction data using Excelizer.
#   Starts from row 2 to preserve headers in row 1.
#   """
#   defp populate_transaction_data_excelizer(file_id, transactions) do
#     Logger.info("Populating #{length(transactions)} transactions starting from row 2")

#     transactions
#     |> Enum.with_index(2)  # Start from row 2 (row 1 has headers)
#     |> Enum.each(fn {transaction, row} ->
#       populate_transaction_row_excelizer(file_id, transaction, row)
#     end)

#     Logger.info("Finished populating transaction data")
#   end

#   @doc """
#   Populate a single transaction row using Excelizer.
#   Maps transaction fields to Excel columns based on the template structure.
#   """
#   defp populate_transaction_row_excelizer(file_id, transaction, row) do
#     try do
#       # Column mapping based on template structure
#       # A: Transaction Date, B: Transaction ID, C: Description, etc.
#       set_cell_value_safe(file_id, "A#{row}", format_date_for_export(transaction.transaction_date))
#       set_cell_value_safe(file_id, "B#{row}", safe_string(transaction.transaction_id))
#       set_cell_value_safe(file_id, "C#{row}", safe_string(transaction.description))
#       set_cell_value_safe(file_id, "D#{row}", safe_string(transaction.reference))
#       set_cell_value_safe(file_id, "E#{row}", safe_number(transaction.amount))
#       set_cell_value_safe(file_id, "F#{row}", safe_string(transaction.transaction_type))
#       set_cell_value_safe(file_id, "G#{row}", safe_string(transaction.account))
#       set_cell_value_safe(file_id, "H#{row}", safe_string(transaction.category))
#       set_cell_value_safe(file_id, "I#{row}", safe_string(transaction.currency))
#       set_cell_value_safe(file_id, "J#{row}", if(transaction.is_matched, do: "Yes", else: "No"))
#       set_cell_value_safe(file_id, "K#{row}", safe_number(transaction.match_confidence))
#       set_cell_value_safe(file_id, "L#{row}", safe_string(get_source_file_name(transaction)))
#       set_cell_value_safe(file_id, "M#{row}", safe_string(get_file_type(transaction)))
#       set_cell_value_safe(file_id, "N#{row}", format_date_for_export(transaction.inserted_at))

#     rescue
#       error ->
#         Logger.error("Failed to populate row #{row} with Excelizer: #{inspect(error)}")
#         # Continue with next row rather than failing completely
#     end
#   end

#   # Safely set cell value using Excelizer with correct API
#   defp set_cell_value_safe(file_id, cell_address, value) do
#     try do
#       # Use the correct Excelizer Native Base API
#       # set_cell_value(file_id, sheet_name, column, value_type, value)
#       {value_type, formatted_value} = format_value_for_excelizer(value)
#       # Try to get the first sheet name, fallback to common names
#       sheet_name = get_first_sheet_name(file_id)

#       Logger.debug("Setting cell #{cell_address} on sheet '#{sheet_name}' with value: #{inspect(formatted_value)} (type: #{value_type})")

#       case Excelizer.Native.Base.set_cell_value(file_id, sheet_name, cell_address, value_type, formatted_value) do
#         {:ok, _} ->
#           :ok
#         {:error, reason} ->
#           Logger.warn("Failed to set cell #{cell_address} on sheet '#{sheet_name}': #{inspect(reason)}")
#       end
#     rescue
#       error ->
#         Logger.warn("Failed to set cell #{cell_address}: #{inspect(error)}")
#     end
#   end

#   # Get the first sheet name from the workbook
#   defp get_first_sheet_name(file_id) do
#     # Try to get the first sheet name, fallback to common names
#     case Excelizer.Native.Base.get_sheet_name(file_id, 0) do
#       {:ok, sheet_name} when is_binary(sheet_name) and sheet_name != "" ->
#         Logger.info("Found sheet name: #{sheet_name}")
#         sheet_name
#       _ ->
#         # Try common sheet names in order of likelihood
#         sheet_names = ["Sheet1", "sheet1", "Worksheet", "Data", "Transactions"]

#         found_sheet = Enum.find(sheet_names, fn sheet_name ->
#           case Excelizer.Native.Base.get_sheet_index(file_id, sheet_name) do
#             {:ok, _index} ->
#               Logger.info("Found existing sheet: #{sheet_name}")
#               true
#             _ -> false
#           end
#         end)

#         found_sheet || "Sheet1"
#     end
#   end

#   # Format value and determine type for Excelizer
#   # Supported types: "string", "float", "int", "boolean", "datetime", "nil"
#   defp format_value_for_excelizer(%Decimal{} = decimal), do: {"float", Decimal.to_float(decimal)}
#   defp format_value_for_excelizer(value) when is_float(value), do: {"float", value}
#   defp format_value_for_excelizer(value) when is_integer(value), do: {"int", value}
#   defp format_value_for_excelizer(value) when is_binary(value), do: {"string", value}
#   defp format_value_for_excelizer(true), do: {"boolean", "true"}
#   defp format_value_for_excelizer(false), do: {"boolean", "false"}
#   defp format_value_for_excelizer(nil), do: {"nil", nil}
#   defp format_value_for_excelizer(value), do: {"string", to_string(value)}

#   # Helper functions for data formatting and safety

#   # Safely convert to string
#   defp safe_string(nil), do: ""
#   defp safe_string(value) when is_binary(value), do: value
#   defp safe_string(value), do: to_string(value)

#   # Safely convert to number
#   defp safe_number(nil), do: 0
#   defp safe_number(%Decimal{} = decimal), do: Decimal.to_float(decimal)
#   defp safe_number(value) when is_number(value), do: value
#   defp safe_number(_), do: 0

#   # Get source file name from uploaded_file association
#   defp get_source_file_name(%{uploaded_file: %{filename: filename}}) when is_binary(filename), do: filename
#   defp get_source_file_name(%{uploaded_file: %{original_filename: filename}}) when is_binary(filename), do: filename
#   defp get_source_file_name(_), do: ""

#   # Get file type from uploaded_file association
#   defp get_file_type(%{uploaded_file: %{file_type: file_type}}) when is_binary(file_type), do: file_type
#   defp get_file_type(_), do: ""

#   # Format dates for Excel export
#   defp format_date_for_export(nil), do: ""
#   defp format_date_for_export(%Date{} = date), do: Date.to_string(date)
#   defp format_date_for_export(%DateTime{} = datetime), do: DateTime.to_date(datetime) |> Date.to_string()
#   defp format_date_for_export(%NaiveDateTime{} = naive_datetime), do: NaiveDateTime.to_date(naive_datetime) |> Date.to_string()

#   @doc """
#   Create a new workbook with Excelizer (fallback when template is not available).
#   """
#   defp create_new_workbook_with_excelizer(transactions, output_path) do
#     try do
#       Logger.info("Creating new workbook with Excelizer: #{output_path}")

#       case Excelizer.new(output_path, fn file_id ->
#         Logger.info("New workbook created with file_id: #{inspect(file_id)}")

#         # Create headers
#         create_headers_excelizer(file_id, "Sheet1")

#         # Populate the sheet with transaction data
#         populate_transaction_data_excelizer(file_id, transactions)

#         Logger.info("Data populated in new workbook")
#       end) do
#         :ok ->
#           Logger.info("Successfully created new workbook with Excelizer: #{output_path}")
#           {:ok, output_path}

#         {:error, reason} ->
#           Logger.error("Failed to create new workbook with Excelizer: #{inspect(reason)}")
#           {:error, "Failed to create new workbook: #{inspect(reason)}"}
#       end

#     rescue
#       error ->
#         Logger.error("Failed to create new workbook with Excelizer: #{inspect(error)}")
#         {:error, Exception.message(error)}
#     end
#   end

#   @doc """
#   Create headers for the Excel sheet.
#   """
#   defp create_headers_excelizer(file_id, sheet_name) do
#     headers = [
#       {"A1", "Transaction Date"},
#       {"B1", "Transaction ID"},
#       {"C1", "Description"},
#       {"D1", "Reference"},
#       {"E1", "Amount"},
#       {"F1", "Transaction Type"},
#       {"G1", "Account"},
#       {"H1", "Category"},
#       {"I1", "Currency"},
#       {"J1", "Matched"},
#       {"K1", "Match Confidence"},
#       {"L1", "Source File"},
#       {"M1", "File Type"},
#       {"N1", "Import Date"}
#     ]

#     Enum.each(headers, fn {cell, value} ->
#       case Excelizer.Native.Base.set_cell_value(file_id, sheet_name, cell, "string", value) do
#         {:ok, _} -> :ok
#         {:error, reason} -> Logger.warn("Failed to set header #{cell}: #{inspect(reason)}")
#       end
#     end)
#   end

#   @doc """
#   Generate Excel content for reconciliation report using Excelizer.
#   """
#   def generate_reconciliation_report_excel(run, transactions, matches) do
#     try do
#       # For now, return an error since we need to implement this with Excelizer
#       # This would require creating a new workbook rather than using a template
#       {:error, "Reconciliation report with Excelizer not yet implemented"}
#     rescue
#       error ->
#         Logger.error("Failed to generate reconciliation report with Excelizer: #{inspect(error)}")
#         {:error, Exception.message(error)}
#     end
#   end
# end










defmodule Reconciliation.Services.ExcelizerExport do
  @moduledoc """
  Optimized Excel export service for large datasets using Excelizer.
  Handles 8,000+ records efficiently with minimal memory footprint.
  """

  require Logger

  @template_filename "transactions_Reconciliation.xlsx"
  @completed_folder "completed"
  @max_rows_before_warn 5000
  @batch_size 500

  @doc """
  Generate Excel content optimized for large datasets.
  """
  def generate_transactions_excel_from_template(transactions, run_name \\ "export") do
    count = length(transactions)
    
    if count > @max_rows_before_warn do
      Logger.warning("Exporting large dataset: #{count} records. This may take time...")
    end

    try do
      project_root = File.cwd!()
      template_path = Path.join(project_root, @template_filename)
      completed_dir = Path.join(project_root, @completed_folder)
      File.mkdir_p!(completed_dir)

      timestamp = DateTime.utc_now() |> DateTime.to_iso8601(:basic) |> String.replace(":", "-")
      completed_filename = "transactions_#{run_name}_#{timestamp}_excelizer.xlsx"
      completed_path = Path.join(completed_dir, completed_filename)

      result = if File.exists?(template_path) do
        populate_template_with_excelizer(template_path, transactions, completed_path, count)
      else
        create_new_workbook_with_excelizer(transactions, completed_path, count)
      end

      case result do
        {:ok, _} -> {:ok, completed_path, completed_filename}
        {:error, reason} -> {:error, reason}
      end

    rescue
      error -> {:error, Exception.message(error)}
    end
  end

  defp populate_template_with_excelizer(template_path, transactions, output_path, count) do
    try do
      case Excelizer.open(template_path, fn file_id ->
        sheet_name = get_first_sheet_name(file_id)
        
        # Process in batches to reduce memory pressure
        transactions
        |> Stream.chunk_every(@batch_size)
        |> Stream.with_index(2, @batch_size)
        |> Enum.each(fn {batch, start_row} ->
          populate_batch(file_id, sheet_name, batch, start_row)
        end)

        case Excelizer.Native.Base.save_as(file_id, output_path) do
          {:ok, _} -> :ok
          {:error, reason} -> {:error, "Save failed: #{inspect(reason)}"}
        end
      end) do
        :ok -> {:ok, output_path}
        {:error, reason} -> {:error, reason}
      end
    rescue
      error -> {:error, Exception.message(error)}
    end
  end

  # Process records in batches for better memory management
  defp populate_batch(file_id, sheet_name, batch, start_row) do
    Enum.reduce(batch, start_row, fn transaction, row ->
      populate_transaction_row_excelizer(file_id, sheet_name, transaction, row)
      row + 1
    end)
  end

  defp populate_transaction_row_excelizer(file_id, sheet_name, transaction, row) do
    set_cell_value_safe(file_id, sheet_name, "A#{row}", format_date(transaction.transaction_date))
    set_cell_value_safe(file_id, sheet_name, "B#{row}", safe_string(transaction.transaction_id))
    set_cell_value_safe(file_id, sheet_name, "C#{row}", safe_string(transaction.description))
    set_cell_value_safe(file_id, sheet_name, "D#{row}", safe_string(transaction.reference))
    set_cell_value_safe(file_id, sheet_name, "E#{row}", safe_number(transaction.amount))
    set_cell_value_safe(file_id, sheet_name, "F#{row}", safe_string(transaction.transaction_type))
    set_cell_value_safe(file_id, sheet_name, "G#{row}", safe_string(transaction.account))
    set_cell_value_safe(file_id, sheet_name, "H#{row}", safe_string(transaction.category))
    set_cell_value_safe(file_id, sheet_name, "I#{row}", safe_string(transaction.currency))
    set_cell_value_safe(file_id, sheet_name, "J#{row}", if(transaction.is_matched, do: "Yes", else: "No"))
    set_cell_value_safe(file_id, sheet_name, "K#{row}", safe_number(transaction.match_confidence))
    set_cell_value_safe(file_id, sheet_name, "L#{row}", get_source_file_name(transaction))
    set_cell_value_safe(file_id, sheet_name, "M#{row}", get_file_type(transaction))
    set_cell_value_safe(file_id, sheet_name, "N#{row}", format_date(transaction.inserted_at))
  rescue
    error -> 
      Logger.error("Row #{row} error: #{inspect(error)}")
      row  # Continue processing next rows
  end

  defp set_cell_value_safe(file_id, sheet_name, cell_address, value) do
    try do
      {value_type, formatted_value} = format_value(value)
      Excelizer.Native.Base.set_cell_value(file_id, sheet_name, cell_address, value_type, formatted_value)
    rescue
      error -> 
        Logger.warn("Cell #{cell_address} error: #{inspect(error)}")
        :error
    end
  end

  defp get_first_sheet_name(file_id) do
    case Excelizer.Native.Base.get_sheet_name(file_id, 0) do
      {:ok, name} -> name
      _ -> "Sheet1"
    end
  end

  # Value formatting
  defp format_value(%Decimal{} = decimal), do: {"float", Decimal.to_float(decimal)}
  defp format_value(value) when is_float(value), do: {"float", value}
  defp format_value(value) when is_integer(value), do: {"int", value}
  defp format_value(true), do: {"boolean", "true"}
  defp format_value(false), do: {"boolean", "false"}
  defp format_value(nil), do: {"nil", nil}
  defp format_value(value) when is_binary(value), do: {"string", value}
  defp format_value(value), do: {"string", to_string(value)}

  # Data safety
  defp safe_string(nil), do: ""
  defp safe_string(value) when is_binary(value), do: value
  defp safe_string(value), do: to_string(value)

  defp safe_number(nil), do: 0
  defp safe_number(%Decimal{} = decimal), do: Decimal.to_float(decimal)
  defp safe_number(value) when is_number(value), do: value
  defp safe_number(_), do: 0

  defp get_source_file_name(%{uploaded_file: %{filename: filename}}), do: safe_string(filename)
  defp get_source_file_name(%{uploaded_file: %{original_filename: filename}}), do: safe_string(filename)
  defp get_source_file_name(_), do: ""

  defp get_file_type(%{uploaded_file: %{file_type: file_type}}), do: safe_string(file_type)
  defp get_file_type(_), do: ""

  defp format_date(nil), do: ""
  defp format_date(%Date{} = date), do: Date.to_string(date)
  defp format_date(%DateTime{} = datetime), do: DateTime.to_date(datetime) |> Date.to_string()
  defp format_date(%NaiveDateTime{} = ndt), do: NaiveDateTime.to_date(ndt) |> Date.to_string()

  defp create_new_workbook_with_excelizer(transactions, output_path, count) do
    try do
      case Excelizer.new(output_path, fn file_id ->
        sheet_name = "Sheet1"
        create_headers_excelizer(file_id, sheet_name)
        
        # Process in batches for new workbooks too
        transactions
        |> Stream.chunk_every(@batch_size)
        |> Stream.with_index(2, @batch_size)
        |> Enum.each(fn {batch, start_row} ->
          populate_batch(file_id, sheet_name, batch, start_row)
        end)

        Excelizer.Native.Base.save_as(file_id, output_path)
      end) do
        {:ok, _} -> {:ok, output_path}
        {:error, reason} -> {:error, inspect(reason)}
      end
    rescue
      error -> {:error, Exception.message(error)}
    end
  end

  defp create_headers_excelizer(file_id, sheet_name) do
    headers = %{
      "A1" => "Transaction Date",
      "B1" => "Transaction ID",
      "C1" => "Description",
      "D1" => "Reference",
      "E1" => "Amount",
      "F1" => "Transaction Type",
      "G1" => "Account",
      "H1" => "Category",
      "I1" => "Currency",
      "J1" => "Matched",
      "K1" => "Match Confidence",
      "L1" => "Source File",
      "M1" => "File Type",
      "N1" => "Import Date"
    }

    Enum.each(headers, fn {cell, value} ->
      Excelizer.Native.Base.set_cell_value(file_id, sheet_name, cell, "string", value)
    end)
  end

  def generate_reconciliation_report_excel(_run, _transactions, _matches) do
    {:error, "Not implemented"}
  end
end